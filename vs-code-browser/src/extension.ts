import * as vscode from 'vscode';
import { <PERSON><PERSON>er<PERSON>rovider } from './browserProvider';

export function activate(context: vscode.ExtensionContext) {
	console.log('VS Code Browser extension is now active!');

	// Create browser provider
	const browserProvider = new BrowserProvider(context.extensionUri);

	// Register the webview provider
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider(BrowserProvider.viewType, browserProvider)
	);

	// Set context to show browser view
	vscode.commands.executeCommand('setContext', 'browserViewVisible', true);

	// Register commands
	const openBrowserCommand = vscode.commands.registerCommand('vs-code-browser.openBrowser', () => {
		// Focus on the browser panel
		vscode.commands.executeCommand('workbench.view.extension.browserPanel');
	});

	const openUrlCommand = vscode.commands.registerCommand('vs-code-browser.openUrl', async () => {
		const url = await vscode.window.showInputBox({
			prompt: 'Enter URL to open',
			placeHolder: 'https://example.com or localhost:3000',
			value: 'https://'
		});

		if (url) {
			browserProvider.createNewTab(url);
			vscode.commands.executeCommand('workbench.view.extension.browserPanel');
		}
	});

	const newTabCommand = vscode.commands.registerCommand('vs-code-browser.newTab', async () => {
		const url = await vscode.window.showInputBox({
			prompt: 'Enter URL for new tab',
			placeHolder: 'https://example.com or localhost:3000',
			value: 'https://www.google.com'
		});

		if (url) {
			browserProvider.createNewTab(url);
			vscode.commands.executeCommand('workbench.view.extension.browserPanel');
		}
	});

	const openLocalhostCommand = vscode.commands.registerCommand('vs-code-browser.openLocalhost', async () => {
		const port = await vscode.window.showInputBox({
			prompt: 'Enter localhost port',
			placeHolder: '3000, 8080, 5000, etc.',
			value: '3000'
		});

		if (port) {
			const url = `http://localhost:${port}`;
			browserProvider.createNewTab(url);
			vscode.commands.executeCommand('workbench.view.extension.browserPanel');
		}
	});

	// Add quick access commands for common development URLs
	const openGoogleCommand = vscode.commands.registerCommand('vs-code-browser.openGoogle', () => {
		browserProvider.createNewTab('https://www.google.com');
		vscode.commands.executeCommand('workbench.view.extension.browserPanel');
	});

	const openGitHubCommand = vscode.commands.registerCommand('vs-code-browser.openGitHub', () => {
		browserProvider.createNewTab('https://github.com');
		vscode.commands.executeCommand('workbench.view.extension.browserPanel');
	});

	const openStackOverflowCommand = vscode.commands.registerCommand('vs-code-browser.openStackOverflow', () => {
		browserProvider.createNewTab('https://stackoverflow.com');
		vscode.commands.executeCommand('workbench.view.extension.browserPanel');
	});

	const openChatGPTCommand = vscode.commands.registerCommand('vs-code-browser.openChatGPT', () => {
		browserProvider.createNewTab('https://chat.openai.com');
		vscode.commands.executeCommand('workbench.view.extension.browserPanel');
	});

	// Add all commands to subscriptions
	context.subscriptions.push(
		openBrowserCommand,
		openUrlCommand,
		newTabCommand,
		openLocalhostCommand,
		openGoogleCommand,
		openGitHubCommand,
		openStackOverflowCommand,
		openChatGPTCommand
	);
}

export function deactivate() {}
