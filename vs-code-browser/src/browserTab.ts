import * as vscode from 'vscode';

export class BrowserTab {
    private _url: string;
    private _title: string;
    private _history: string[] = [];
    private _historyIndex: number = -1;
    private _canGoBack: boolean = false;
    private _canGoForward: boolean = false;

    constructor(
        private readonly _id: string,
        initialUrl: string,
        private readonly _extensionUri: vscode.Uri
    ) {
        this._url = this._normalizeUrl(initialUrl);
        this._title = this._getTitleFromUrl(this._url);
        this._addToHistory(this._url);
    }

    public getId(): string {
        return this._id;
    }

    public getUrl(): string {
        return this._url;
    }

    public getTitle(): string {
        return this._title;
    }

    public canGoBack(): boolean {
        return this._canGoBack;
    }

    public canGoForward(): boolean {
        return this._canGoForward;
    }

    public navigate(url: string): void {
        const normalizedUrl = this._normalizeUrl(url);
        this._url = normalizedUrl;
        this._title = this._getTitleFromUrl(normalizedUrl);
        this._addToHistory(normalizedUrl);
        this._updateNavigationState();
    }

    public goBack(): boolean {
        if (this._canGoBack && this._historyIndex > 0) {
            this._historyIndex--;
            this._url = this._history[this._historyIndex];
            this._title = this._getTitleFromUrl(this._url);
            this._updateNavigationState();
            return true;
        }
        return false;
    }

    public goForward(): boolean {
        if (this._canGoForward && this._historyIndex < this._history.length - 1) {
            this._historyIndex++;
            this._url = this._history[this._historyIndex];
            this._title = this._getTitleFromUrl(this._url);
            this._updateNavigationState();
            return true;
        }
        return false;
    }

    public refresh(): void {
        // The URL stays the same, just trigger a refresh
        // This will be handled by the webview
    }

    private _normalizeUrl(url: string): string {
        // Handle common URL patterns
        if (!url) {
            return 'https://www.google.com';
        }

        // If it's already a complete URL, return as is
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('file://')) {
            return url;
        }

        // Handle localhost patterns
        if (url.startsWith('localhost') || url.match(/^localhost:\d+/)) {
            return `http://${url}`;
        }

        // Handle IP addresses
        if (url.match(/^\d+\.\d+\.\d+\.\d+(:\d+)?$/)) {
            return `http://${url}`;
        }

        // Handle port-only patterns (assume localhost)
        if (url.match(/^:\d+$/)) {
            return `http://localhost${url}`;
        }

        // Handle just port numbers
        if (url.match(/^\d+$/) && parseInt(url) > 1000 && parseInt(url) < 65536) {
            return `http://localhost:${url}`;
        }

        // If it looks like a domain, add https
        if (url.includes('.') && !url.includes(' ')) {
            return `https://${url}`;
        }

        // Otherwise, treat as a search query
        return `https://www.google.com/search?q=${encodeURIComponent(url)}`;
    }

    private _getTitleFromUrl(url: string): string {
        try {
            const urlObj = new URL(url);
            
            // Special cases for common sites
            if (urlObj.hostname === 'www.google.com' || urlObj.hostname === 'google.com') {
                if (urlObj.pathname === '/search') {
                    const query = urlObj.searchParams.get('q');
                    return query ? `Google - ${query}` : 'Google Search';
                }
                return 'Google';
            }

            if (urlObj.hostname.includes('localhost') || urlObj.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
                return `Localhost${urlObj.port ? ':' + urlObj.port : ''}`;
            }

            // Extract domain name
            const hostname = urlObj.hostname.replace(/^www\./, '');
            const parts = hostname.split('.');
            if (parts.length > 1) {
                return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
            }

            return hostname;
        } catch (error) {
            return 'New Tab';
        }
    }

    private _addToHistory(url: string): void {
        // Remove any forward history if we're navigating to a new page
        if (this._historyIndex < this._history.length - 1) {
            this._history = this._history.slice(0, this._historyIndex + 1);
        }

        // Add new URL to history
        this._history.push(url);
        this._historyIndex = this._history.length - 1;

        // Limit history size
        if (this._history.length > 50) {
            this._history = this._history.slice(-50);
            this._historyIndex = this._history.length - 1;
        }
    }

    private _updateNavigationState(): void {
        this._canGoBack = this._historyIndex > 0;
        this._canGoForward = this._historyIndex < this._history.length - 1;
    }

    public getHistoryInfo(): { canGoBack: boolean; canGoForward: boolean; currentIndex: number; historyLength: number } {
        return {
            canGoBack: this._canGoBack,
            canGoForward: this._canGoForward,
            currentIndex: this._historyIndex,
            historyLength: this._history.length
        };
    }

    public getHistory(): string[] {
        return [...this._history];
    }
}
