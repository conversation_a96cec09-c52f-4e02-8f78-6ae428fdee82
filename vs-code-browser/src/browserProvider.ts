import * as vscode from 'vscode';
import { BrowserTab } from './browserTab';

export class BrowserProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'browserView';
    private _view?: vscode.WebviewView;
    private _tabs: Map<string, BrowserTab> = new Map();
    private _activeTabId?: string;
    private _tabCounter = 0;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            enableForms: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        webviewView.webview.onDidReceiveMessage(data => {
            this._handleMessage(data);
        });

        // Create initial tab
        this.createNewTab('https://www.google.com');
    }

    private _handleMessage(data: any) {
        switch (data.type) {
            case 'navigate':
                this._navigateActiveTab(data.url);
                break;
            case 'back':
                this._goBack();
                break;
            case 'forward':
                this._goForward();
                break;
            case 'refresh':
                this._refresh();
                break;
            case 'newTab':
                this.createNewTab(data.url || 'https://www.google.com');
                break;
            case 'closeTab':
                this._closeTab(data.tabId);
                break;
            case 'switchTab':
                this._switchTab(data.tabId);
                break;
            case 'toggleDevTools':
                this._toggleDevTools();
                break;
        }
    }

    public createNewTab(url: string): string {
        const tabId = `tab-${++this._tabCounter}`;
        const tab = new BrowserTab(tabId, url, this._extensionUri);
        this._tabs.set(tabId, tab);
        this._activeTabId = tabId;
        this._updateUI();
        return tabId;
    }

    private _navigateActiveTab(url: string) {
        console.log('Navigating to:', url);
        if (this._activeTabId) {
            const tab = this._tabs.get(this._activeTabId);
            if (tab) {
                tab.navigate(url);
                console.log('Tab navigated to:', tab.getUrl());
                this._updateUI();
            }
        }
    }

    private _goBack() {
        if (this._activeTabId) {
            const tab = this._tabs.get(this._activeTabId);
            if (tab) {
                tab.goBack();
            }
        }
    }

    private _goForward() {
        if (this._activeTabId) {
            const tab = this._tabs.get(this._activeTabId);
            if (tab) {
                tab.goForward();
            }
        }
    }

    private _refresh() {
        if (this._activeTabId) {
            const tab = this._tabs.get(this._activeTabId);
            if (tab) {
                tab.refresh();
            }
        }
    }

    private _closeTab(tabId: string) {
        this._tabs.delete(tabId);
        if (this._activeTabId === tabId) {
            const remainingTabs = Array.from(this._tabs.keys());
            this._activeTabId = remainingTabs.length > 0 ? remainingTabs[0] : undefined;
        }
        if (this._tabs.size === 0) {
            this.createNewTab('https://www.google.com');
        }
        this._updateUI();
    }

    private _switchTab(tabId: string) {
        if (this._tabs.has(tabId)) {
            this._activeTabId = tabId;
            this._updateUI();
        }
    }

    private _toggleDevTools() {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'toggleDevTools'
            });
        }
    }

    private _updateUI() {
        if (this._view) {
            const tabs = Array.from(this._tabs.entries()).map(([id, tab]) => ({
                id,
                title: tab.getTitle(),
                url: tab.getUrl(),
                isActive: id === this._activeTabId
            }));

            const activeTab = this._activeTabId ? this._tabs.get(this._activeTabId) : null;
            const navigationState = activeTab ? activeTab.getHistoryInfo() : {
                canGoBack: false,
                canGoForward: false,
                currentIndex: 0,
                historyLength: 0
            };

            this._view.webview.postMessage({
                type: 'updateTabs',
                tabs,
                activeTabId: this._activeTabId,
                navigationState
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; frame-src https: http: data:; script-src 'unsafe-inline'; style-src 'unsafe-inline';">
            <title>Browser</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: var(--vscode-font-family);
                    background-color: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }
                
                .browser-container {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                }
                
                .tab-bar {
                    display: flex;
                    background-color: var(--vscode-tab-inactiveBackground);
                    border-bottom: 1px solid var(--vscode-tab-border);
                    overflow-x: auto;
                }
                
                .tab {
                    display: flex;
                    align-items: center;
                    padding: 8px 12px;
                    border-right: 1px solid var(--vscode-tab-border);
                    cursor: pointer;
                    min-width: 120px;
                    max-width: 200px;
                    background-color: var(--vscode-tab-inactiveBackground);
                }
                
                .tab.active {
                    background-color: var(--vscode-tab-activeBackground);
                }
                
                .tab-title {
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 12px;
                }
                
                .tab-close {
                    margin-left: 8px;
                    cursor: pointer;
                    opacity: 0.7;
                }
                
                .tab-close:hover {
                    opacity: 1;
                }
                
                .navigation-bar {
                    display: flex;
                    align-items: center;
                    padding: 8px;
                    background-color: var(--vscode-editor-background);
                    border-bottom: 1px solid var(--vscode-widget-border);
                }
                
                .nav-button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 6px 12px;
                    margin-right: 4px;
                    cursor: pointer;
                    border-radius: 2px;
                }
                
                .nav-button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
                
                .nav-button:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    background: var(--vscode-button-secondaryBackground);
                }

                .loading-indicator {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: var(--vscode-editor-background);
                    padding: 20px;
                    border-radius: 4px;
                    border: 1px solid var(--vscode-widget-border);
                    z-index: 1000;
                }
                
                .url-input {
                    flex: 1;
                    margin: 0 8px;
                    padding: 6px 12px;
                    background: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 2px;
                }
                
                .browser-content {
                    flex: 1;
                    position: relative;
                    background: white;
                }
                
                .browser-frame {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
                
                .new-tab-button {
                    padding: 8px 12px;
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    cursor: pointer;
                }
                
                .new-tab-button:hover {
                    background: var(--vscode-button-hoverBackground);
                }

                .dev-tools-panel {
                    height: 200px;
                    background: var(--vscode-editor-background);
                    border-top: 1px solid var(--vscode-widget-border);
                    display: none;
                    flex-direction: column;
                }

                .dev-tools-header {
                    padding: 8px 12px;
                    background: var(--vscode-tab-inactiveBackground);
                    border-bottom: 1px solid var(--vscode-tab-border);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .dev-tools-content {
                    flex: 1;
                    padding: 12px;
                    overflow-y: auto;
                    font-family: monospace;
                    font-size: 12px;
                }

                .dev-tools-close {
                    cursor: pointer;
                    opacity: 0.7;
                }

                .dev-tools-close:hover {
                    opacity: 1;
                }
            </style>
        </head>
        <body>
            <div class="browser-container">
                <div class="tab-bar">
                    <div id="tabs-container"></div>
                    <button class="new-tab-button" onclick="createNewTab()">+</button>
                </div>
                
                <div class="navigation-bar">
                    <button class="nav-button" id="back-btn" onclick="goBack()">←</button>
                    <button class="nav-button" id="forward-btn" onclick="goForward()">→</button>
                    <button class="nav-button" onclick="refresh()">⟳</button>
                    <input type="text" class="url-input" id="url-input" placeholder="Enter URL..." onkeypress="handleUrlKeyPress(event)">
                    <button class="nav-button" onclick="navigate()">Go</button>
                    <button class="nav-button" onclick="toggleDevTools()">🔧</button>
                </div>
                
                <div class="browser-content">
                    <div id="browser-frame-container" class="browser-frame" style="position: relative;">
                        <div id="loading-indicator" class="loading-indicator" style="display: none;">
                            <div>Loading...</div>
                        </div>
                        <iframe id="browser-frame" class="browser-frame" src="about:blank" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"></iframe>
                    </div>
                </div>

                <div id="dev-tools-panel" class="dev-tools-panel">
                    <div class="dev-tools-header">
                        <span>Developer Tools</span>
                        <span class="dev-tools-close" onclick="toggleDevTools()">×</span>
                    </div>
                    <div class="dev-tools-content">
                        <div>Console output will appear here...</div>
                        <div>Network requests, DOM inspection, and other developer tools features would be implemented here.</div>
                        <div>Note: Full developer tools require additional browser engine integration.</div>
                    </div>
                </div>
            </div>
            
            <script>
                const vscode = acquireVsCodeApi();
                let currentTabs = [];
                let activeTabId = null;
                
                function handleUrlKeyPress(event) {
                    if (event.key === 'Enter') {
                        navigate();
                    }
                }
                
                function navigate() {
                    const url = document.getElementById('url-input').value;
                    console.log('Navigate called with URL:', url);
                    if (url) {
                        // Show loading indicator
                        document.getElementById('loading-indicator').style.display = 'block';

                        vscode.postMessage({
                            type: 'navigate',
                            url: url
                        });
                        console.log('Message sent to extension');
                    }
                }
                
                function goBack() {
                    vscode.postMessage({ type: 'back' });
                }
                
                function goForward() {
                    vscode.postMessage({ type: 'forward' });
                }
                
                function refresh() {
                    vscode.postMessage({ type: 'refresh' });
                }
                
                function createNewTab() {
                    vscode.postMessage({ type: 'newTab' });
                }
                
                function closeTab(tabId) {
                    vscode.postMessage({ 
                        type: 'closeTab',
                        tabId: tabId
                    });
                }
                
                function switchTab(tabId) {
                    vscode.postMessage({
                        type: 'switchTab',
                        tabId: tabId
                    });
                }
                
                function toggleDevTools() {
                    const devToolsPanel = document.getElementById('dev-tools-panel');
                    const browserContent = document.querySelector('.browser-content');

                    if (devToolsPanel.style.display === 'none' || devToolsPanel.style.display === '') {
                        devToolsPanel.style.display = 'flex';
                        browserContent.style.height = 'calc(100% - 200px)';
                    } else {
                        devToolsPanel.style.display = 'none';
                        browserContent.style.height = '100%';
                    }

                    vscode.postMessage({ type: 'toggleDevTools' });
                }
                
                function updateTabs(tabs, activeId, navigationState) {
                    currentTabs = tabs;
                    activeTabId = activeId;

                    const container = document.getElementById('tabs-container');
                    container.innerHTML = '';

                    tabs.forEach(tab => {
                        const tabElement = document.createElement('div');
                        tabElement.className = 'tab' + (tab.isActive ? ' active' : '');
                        tabElement.onclick = () => switchTab(tab.id);

                        tabElement.innerHTML = \`
                            <span class="tab-title" title="\${tab.title}">\${tab.title}</span>
                            <span class="tab-close" onclick="event.stopPropagation(); closeTab('\${tab.id}')">×</span>
                        \`;

                        container.appendChild(tabElement);
                    });

                    // Update navigation button states
                    if (navigationState) {
                        document.getElementById('back-btn').disabled = !navigationState.canGoBack;
                        document.getElementById('forward-btn').disabled = !navigationState.canGoForward;
                    }

                    // Update URL input and iframe with active tab URL
                    const activeTab = tabs.find(t => t.isActive);
                    if (activeTab) {
                        document.getElementById('url-input').value = activeTab.url;
                        const iframe = document.getElementById('browser-frame');

                        // Only update iframe src if it's different to avoid unnecessary reloads
                        if (iframe.src !== activeTab.url) {
                            document.getElementById('loading-indicator').style.display = 'block';
                            iframe.src = activeTab.url;

                            // Hide loading indicator when iframe loads
                            iframe.onload = () => {
                                document.getElementById('loading-indicator').style.display = 'none';
                            };

                            iframe.onerror = () => {
                                document.getElementById('loading-indicator').style.display = 'none';
                            };
                        }
                    }
                }
                
                // Listen for messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'updateTabs':
                            updateTabs(message.tabs, message.activeTabId, message.navigationState);
                            break;
                        case 'toggleDevTools':
                            // Developer tools toggle is handled in the toggleDevTools function
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }
}
