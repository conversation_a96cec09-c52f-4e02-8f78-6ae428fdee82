{"version": 3, "file": "browserProvider.js", "sourceRoot": "", "sources": ["../src/browserProvider.ts"], "names": [], "mappings": ";;;AACA,6CAA0C;AAE1C,MAAa,eAAe;IAOK;IANtB,MAAM,CAAU,QAAQ,GAAG,aAAa,CAAC;IACxC,KAAK,CAAsB;IAC3B,KAAK,GAA4B,IAAI,GAAG,EAAE,CAAC;IAC3C,YAAY,CAAU;IACtB,WAAW,GAAG,CAAC,CAAC;IAExB,YAA6B,aAAyB;QAAzB,kBAAa,GAAb,aAAa,CAAY;IAAG,CAAC;IAEnD,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;SAC3C,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;IAChD,CAAC;IAEO,cAAc,CAAC,IAAS;QAC5B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,KAAK,UAAU;gBACX,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,wBAAwB,CAAC,CAAC;gBACxD,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;YACV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;QACd,CAAC;IACL,CAAC;IAEM,YAAY,CAAC,GAAW;QAC3B,MAAM,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,IAAI,uBAAU,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,GAAW;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,IAAI,GAAG,EAAE,CAAC;gBACN,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAClB,IAAI,CAAC,SAAS,EAAE,CAAC;YACrB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,OAAO;QACX,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,IAAI,GAAG,EAAE,CAAC;gBACN,GAAG,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,IAAI,GAAG,EAAE,CAAC;gBACN,GAAG,CAAC,SAAS,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,QAAQ;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,IAAI,GAAG,EAAE,CAAC;gBACN,GAAG,CAAC,OAAO,EAAE,CAAC;YAClB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,KAAa;QAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAChF,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEO,UAAU,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,gBAAgB;aACzB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,SAAS;QACb,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC9D,EAAE;gBACF,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE;gBACrB,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE;gBACjB,QAAQ,EAAE,EAAE,KAAK,IAAI,CAAC,YAAY;aACrC,CAAC,CAAC,CAAC;YAEJ,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC/E,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBAC7D,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,KAAK;gBACnB,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,CAAC;aACnB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,YAAY;gBAClB,IAAI;gBACJ,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,eAAe;aAClB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAuVC,CAAC;IACb,CAAC;;AAxfL,0CAyfC"}