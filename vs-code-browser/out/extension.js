"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const browserProvider_1 = require("./browserProvider");
function activate(context) {
    console.log('VS Code Browser extension is now active!');
    // Create browser provider
    const browserProvider = new browserProvider_1.BrowserProvider(context.extensionUri);
    // Register the webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(browserProvider_1.BrowserProvider.viewType, browserProvider));
    // Set context to show browser view
    vscode.commands.executeCommand('setContext', 'browserViewVisible', true);
    // Register commands
    const openBrowserCommand = vscode.commands.registerCommand('vs-code-browser.openBrowser', () => {
        // Focus on the browser panel
        vscode.commands.executeCommand('workbench.view.extension.browserPanel');
    });
    const openUrlCommand = vscode.commands.registerCommand('vs-code-browser.openUrl', async () => {
        const url = await vscode.window.showInputBox({
            prompt: 'Enter URL to open',
            placeHolder: 'https://example.com or localhost:3000',
            value: 'https://'
        });
        if (url) {
            browserProvider.createNewTab(url);
            vscode.commands.executeCommand('workbench.view.extension.browserPanel');
        }
    });
    const newTabCommand = vscode.commands.registerCommand('vs-code-browser.newTab', async () => {
        const url = await vscode.window.showInputBox({
            prompt: 'Enter URL for new tab',
            placeHolder: 'https://example.com or localhost:3000',
            value: 'https://www.google.com'
        });
        if (url) {
            browserProvider.createNewTab(url);
            vscode.commands.executeCommand('workbench.view.extension.browserPanel');
        }
    });
    const openLocalhostCommand = vscode.commands.registerCommand('vs-code-browser.openLocalhost', async () => {
        const port = await vscode.window.showInputBox({
            prompt: 'Enter localhost port',
            placeHolder: '3000, 8080, 5000, etc.',
            value: '3000'
        });
        if (port) {
            const url = `http://localhost:${port}`;
            browserProvider.createNewTab(url);
            vscode.commands.executeCommand('workbench.view.extension.browserPanel');
        }
    });
    // Add quick access commands for common development URLs
    const openGoogleCommand = vscode.commands.registerCommand('vs-code-browser.openGoogle', () => {
        browserProvider.createNewTab('https://www.google.com');
        vscode.commands.executeCommand('workbench.view.extension.browserPanel');
    });
    const openGitHubCommand = vscode.commands.registerCommand('vs-code-browser.openGitHub', () => {
        browserProvider.createNewTab('https://github.com');
        vscode.commands.executeCommand('workbench.view.extension.browserPanel');
    });
    const openStackOverflowCommand = vscode.commands.registerCommand('vs-code-browser.openStackOverflow', () => {
        browserProvider.createNewTab('https://stackoverflow.com');
        vscode.commands.executeCommand('workbench.view.extension.browserPanel');
    });
    const openChatGPTCommand = vscode.commands.registerCommand('vs-code-browser.openChatGPT', () => {
        browserProvider.createNewTab('https://chat.openai.com');
        vscode.commands.executeCommand('workbench.view.extension.browserPanel');
    });
    // Add all commands to subscriptions
    context.subscriptions.push(openBrowserCommand, openUrlCommand, newTabCommand, openLocalhostCommand, openGoogleCommand, openGitHubCommand, openStackOverflowCommand, openChatGPTCommand);
}
function deactivate() { }
//# sourceMappingURL=extension.js.map