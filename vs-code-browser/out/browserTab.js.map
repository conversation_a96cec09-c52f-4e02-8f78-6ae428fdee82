{"version": 3, "file": "browserTab.js", "sourceRoot": "", "sources": ["../src/browserTab.ts"], "names": [], "mappings": ";;;AAEA,MAAa,UAAU;IASE;IAEA;IAVb,IAAI,CAAS;IACb,MAAM,CAAS;IACf,QAAQ,GAAa,EAAE,CAAC;IACxB,aAAa,GAAW,CAAC,CAAC,CAAC;IAC3B,UAAU,GAAY,KAAK,CAAC;IAC5B,aAAa,GAAY,KAAK,CAAC;IAEvC,YACqB,GAAW,EAC5B,UAAkB,EACD,aAAyB;QAFzB,QAAG,GAAH,GAAG,CAAQ;QAEX,kBAAa,GAAb,aAAa,CAAY;QAE1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAEM,MAAM;QACT,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEM,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAEM,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,QAAQ,CAAC,GAAW;QACvB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEM,MAAM;QACT,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,SAAS;QACZ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,OAAO;QACV,iDAAiD;QACjD,sCAAsC;IAC1C,CAAC;IAEO,aAAa,CAAC,GAAW;QAC7B,6BAA6B;QAC7B,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO,wBAAwB,CAAC;QACpC,CAAC;QAED,+CAA+C;QAC/C,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACvF,OAAO,GAAG,CAAC;QACf,CAAC;QAED,4BAA4B;QAC5B,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC7D,OAAO,UAAU,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,sBAAsB;QACtB,IAAI,GAAG,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC;YAC3C,OAAO,UAAU,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,+CAA+C;QAC/C,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtB,OAAO,mBAAmB,GAAG,EAAE,CAAC;QACpC,CAAC;QAED,2BAA2B;QAC3B,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC;YACtE,OAAO,oBAAoB,GAAG,EAAE,CAAC;QACrC,CAAC;QAED,uCAAuC;QACvC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,WAAW,GAAG,EAAE,CAAC;QAC5B,CAAC;QAED,qCAAqC;QACrC,OAAO,mCAAmC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,gBAAgB,CAAC,GAAW;QAChC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAE5B,iCAAiC;YACjC,IAAI,MAAM,CAAC,QAAQ,KAAK,gBAAgB,IAAI,MAAM,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC3E,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAChC,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC3C,OAAO,KAAK,CAAC,CAAC,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC;gBACzD,CAAC;gBACD,OAAO,QAAQ,CAAC;YACpB,CAAC;YAED,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACzF,OAAO,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAC9D,CAAC;YAED,sBAAsB;YACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,GAAW;QAC7B,+DAA+D;QAC/D,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACnE,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAE9C,qBAAqB;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAEO,sBAAsB;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC;IAEM,cAAc;QACjB,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;SACtC,CAAC;IACN,CAAC;IAEM,UAAU;QACb,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;CACJ;AAjLD,gCAiLC"}