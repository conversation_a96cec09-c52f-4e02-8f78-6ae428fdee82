{"name": "vs-code-browser", "displayName": "VS Code Browser", "description": "A full-featured web browser integrated into VS Code with multiple tabs, navigation controls, and developer tools", "version": "0.0.1", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "vs-code-browser.openBrowser", "title": "Open Browser", "category": "Browser"}, {"command": "vs-code-browser.openUrl", "title": "Open URL in Browser", "category": "Browser"}, {"command": "vs-code-browser.newTab", "title": "New Browser Tab", "category": "Browser"}, {"command": "vs-code-browser.openLocalhost", "title": "Open Localhost", "category": "Browser"}, {"command": "vs-code-browser.openGoogle", "title": "Open Google", "category": "Browser"}, {"command": "vs-code-browser.openGitHub", "title": "Open GitHub", "category": "Browser"}, {"command": "vs-code-browser.openStackOverflow", "title": "Open Stack Overflow", "category": "Browser"}, {"command": "vs-code-browser.openChatGPT", "title": "Open ChatGPT", "category": "Browser"}], "menus": {"commandPalette": [{"command": "vs-code-browser.openBrowser"}, {"command": "vs-code-browser.openUrl"}, {"command": "vs-code-browser.newTab"}, {"command": "vs-code-browser.openLocalhost"}, {"command": "vs-code-browser.openGoogle"}, {"command": "vs-code-browser.openGitHub"}, {"command": "vs-code-browser.openStackOverflow"}, {"command": "vs-code-browser.openChatGPT"}]}, "viewsContainers": {"panel": [{"id": "browserPanel", "title": "Browser", "icon": "$(globe)"}]}, "views": {"browserPanel": [{"type": "webview", "id": "browserView", "name": "Browser", "when": "browserViewVisible"}]}, "keybindings": [{"command": "vs-code-browser.openBrowser", "key": "ctrl+alt+b", "mac": "cmd+alt+b"}, {"command": "vs-code-browser.openUrl", "key": "ctrl+alt+u", "mac": "cmd+alt+u"}, {"command": "vs-code-browser.newTab", "key": "ctrl+alt+t", "mac": "cmd+alt+t"}, {"command": "vs-code-browser.openLocalhost", "key": "ctrl+alt+l", "mac": "cmd+alt+l"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}