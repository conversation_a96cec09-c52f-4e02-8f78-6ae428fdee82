# VS Code Browser Extension

A full-featured web browser integrated directly into Visual Studio Code with multiple tabs, navigation controls, and developer tools.

## Features

- **Multiple Browser Tabs**: Open and manage multiple web pages simultaneously
- **Full Navigation Controls**: Back, forward, refresh, and URL input
- **Developer Tools Panel**: Toggle-able developer tools simulation
- **Command Palette Integration**: Quick access to browser functions via VS Code commands
- **Keyboard Shortcuts**: Fast navigation with customizable key bindings
- **Localhost Support**: Easy access to local development servers
- **Quick Access Commands**: One-click access to popular developer sites

## Installation

1. Clone this repository
2. Open the project in VS Code
3. Run `npm install` to install dependencies
4. Press `F5` to launch the extension in a new Extension Development Host window

## Usage

### Opening the Browser

- **Command Palette**: `Ctrl+Shift+P` → "Browser: Open Browser"
- **Keyboard Shortcut**: `Ctrl+Shift+B` (Windows/Linux) or `Cmd+Shift+B` (Mac)
- **Panel**: Click on the Browser panel in the bottom panel area

### Available Commands

| Command | Shortcut | Description |
|---------|----------|-------------|
| `Browser: Open Browser` | `Ctrl+Shift+B` | Open the browser panel |
| `Browser: Open URL in Browser` | `Ctrl+Shift+U` | Open a specific URL |
| `Browser: New Browser Tab` | `Ctrl+Shift+T` | Create a new browser tab |
| `Browser: Open Localhost` | `Ctrl+Shift+L` | Open a localhost port |
| `Browser: Open Google` | - | Quick access to Google |
| `Browser: Open GitHub` | - | Quick access to GitHub |
| `Browser: Open Stack Overflow` | - | Quick access to Stack Overflow |
| `Browser: Open ChatGPT` | - | Quick access to ChatGPT |

### Browser Controls

- **Navigation**: Use back/forward buttons or browser history
- **URL Bar**: Enter any URL or search term
- **Tab Management**: Click tabs to switch, use × to close
- **Developer Tools**: Click the 🔧 button to toggle developer tools panel
- **New Tab**: Click the + button to create a new tab

### URL Input Features

The URL input supports various formats:
- Full URLs: `https://example.com`
- Domain names: `example.com` (automatically adds https://)
- Localhost: `localhost:3000` or just `3000`
- IP addresses: `***********:8080`
- Search queries: Any text without dots becomes a Google search

## Development

### Building

```bash
npm run compile
```

### Testing

```bash
npm test
```

### Watching for Changes

```bash
npm run watch
```

## Technical Details

- Built with TypeScript
- Uses VS Code Webview API
- Implements iframe-based browsing with security sandboxing
- Supports multiple tab management
- Includes navigation history tracking

## Limitations

- Uses iframe-based browsing (some sites may block iframe embedding)
- Developer tools are simulated (not full browser dev tools)
- Some advanced browser features may not be available
- Cross-origin restrictions apply

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Changelog

### 0.0.1

- Initial release
- Multiple tab support
- Navigation controls
- Developer tools panel
- Command palette integration
- Keyboard shortcuts
- Quick access commands
